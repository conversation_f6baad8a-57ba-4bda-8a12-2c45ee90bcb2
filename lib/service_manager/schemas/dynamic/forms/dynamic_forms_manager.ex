defmodule ServiceManager.Forms.DynamicFormsManager do
  import Ecto.Query
  alias ServiceManager.Repo
  alias ServiceManager.Routing.{DynamicRoute, DynamicRouteWizard}
  alias ServiceManager.Forms.DynamicForm
  alias ServiceManager.Forms.DynamicRouteForm
  alias ServiceManager.Forms.{FormW<PERSON>rd, FormWizardStep, FormWizardStepDataMapping, MobileFormValidationLink, WizardSession}
  alias ServiceManagerWeb.Api.{FormV2Schema, FormFieldV2Schema, Services.Local.MobileFormsV2Service}

  @doc """
  Create a new form definition.
  """
  def create_form(attrs) do
    %DynamicForm{}
    |> DynamicForm.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Link a form to a route.
  """
  def link_form_to_route(route_id, form_id) do
    %DynamicRouteForm{}
    |> DynamicRouteForm.changeset(%{route_id: route_id, form_id: form_id})
    |> Repo.insert()
  end

  @doc """
  Unlink a form from a route.
  """
  def unlink_form_from_route(route_id, form_id) do
    query = from rf in DynamicRouteForm,
            where: rf.route_id == ^route_id and rf.form_id == ^form_id

    Repo.delete_all(query)
  end

  @doc """
  Get all forms linked to a route.
  """
  def get_route_forms(route_id) do
    query = from f in DynamicForm,
            join: rf in DynamicRouteForm, on: rf.form_id == f.id,
            where: rf.route_id == ^route_id,
            select: f

    Repo.all(query)
  end

  @doc """
  Get a form for a specific route and HTTP method.
  """
  def get_form_for_route(route_id, method) do
    query = from f in DynamicForm,
            join: rf in DynamicRouteForm, on: rf.form_id == f.id,
            where: rf.route_id == ^route_id and f.http_method == ^method,
            select: f,
            limit: 1

    Repo.one(query)
  end

  @doc """
  Validate request parameters against a form.
  """
  def validate_request(form, params) do
    # For GET requests, we validate query params
    # For other methods, we validate the request body

    case form.http_method do
      "GET" -> validate_get_params(form, params)
      _ -> validate_request_body(form, params)
    end
  end

  # Validate GET query parameters
  defp validate_get_params(form, params) do
    # Convert string keys to atoms for easier handling if needed
    params = if is_map(params) do
      for {key, val} <- params, into: %{}, do: {key, val}
    else
      params
    end

    # Use the validation schema for validation
    validate_against_schema(form.validation_schema, params)
  end

  # Validate request body (POST, PUT, etc.)
  defp validate_request_body(form, params) do
    # Use the validation schema for validation
    validate_against_schema(form.validation_schema, params)
  end

  # Perform the actual validation against the JSON schema
  defp validate_against_schema(schema, params) do
    # This is a simplified validation. In a real implementation,
    # you would use a proper JSON Schema validator like ex_json_schema

    # For now, we'll do a basic validation of required fields
    required_fields = schema["required"] || []

    missing_fields = Enum.filter(required_fields, fn field ->
      not Map.has_key?(params, field) and not Map.has_key?(params, String.to_atom(field))
    end)

    if Enum.empty?(missing_fields) do
      {:ok, params}
    else
      {:error, "Missing required fields: #{Enum.join(missing_fields, ", ")}"}
    end
  end

  # Format validation errors for better readability
  defp format_validation_errors(errors) do
    Enum.map(errors, fn {path, error} ->
      path_string = path |> Enum.join(".")
      "#{path_string}: #{error}"
    end)
  end

  @doc """
  Generate a JSON Schema from a form definition.
  This is a helper to create the validation_schema based on the form structure.
  """
  def generate_json_schema(form_def) do
    properties = Enum.reduce(form_def["form"]["fields"], %{}, fn field, acc ->
      # Start with basic properties
      field_schema = %{
        "type" => field["type"],
        "description" => Map.get(field, "description", "")
      }

      # Add constraints based on field type
      field_schema = case field["type"] do
        "string" ->
          field_schema
          |> add_constraint(field, "minLength")
          |> add_constraint(field, "maxLength")
          |> add_constraint(field, "format")

        "number" ->
          field_schema
          |> add_constraint(field, "minimum")
          |> add_constraint(field, "maximum")

        "integer" ->
          field_schema
          |> add_constraint(field, "minimum")
          |> add_constraint(field, "maximum")

        _ -> field_schema
      end

      Map.put(acc, field["name"], field_schema)
    end)

    required = form_def["form"]["fields"]
               |> Enum.filter(fn field -> Map.get(field, "required", false) end)
               |> Enum.map(fn field -> field["name"] end)

    %{
      "type" => "object",
      "properties" => properties,
      "required" => required
    }
  end

  # Helper to add constraints to field schema if they exist in the field definition
  defp add_constraint(field_schema, field, constraint_name) do
    case Map.get(field, constraint_name) do
      nil -> field_schema
      value -> Map.put(field_schema, constraint_name, value)
    end
  end

  @doc """
  Create a form with auto-generated JSON Schema.
  """
  def create_form_with_schema(attrs) do
    validation_schema = generate_json_schema(attrs.form)

    attrs = Map.put(attrs, :validation_schema, validation_schema)

    create_form(attrs)
  end

  @doc """
  List all available forms.
  """
  def list_forms do
    Repo.all(DynamicForm)
  end

  @doc """
  Get a form by ID.
  """
  def get_form(id) do
    Repo.get(DynamicForm, id)
  end

  @doc """
  Update a form.
  """
  def update_form(%DynamicForm{} = form, attrs) do
    form
    |> DynamicForm.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Delete a form.
  """
  def delete_form(%DynamicForm{} = form) do
    Repo.delete(form)
  end

  # =============================================================================
  # WIZARD MANAGEMENT FUNCTIONS
  # =============================================================================

  @doc """
  Create a new form wizard.
  """
  def create_wizard(attrs) do
    %FormWizard{}
    |> FormWizard.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  List all wizards.
  """
  def list_wizards do
    FormWizard
    |> preload([:steps, :created_by])
    |> Repo.all()
  end

  @doc """
  Get a wizard by ID with its steps.
  """
  def get_wizard(id) do
    FormWizard
    |> where([w], w.id == ^id)
    |> preload([steps: [:form]])
    |> Repo.one()
  end

  @doc """
  Update a wizard.
  """
  def update_wizard(%FormWizard{} = wizard, attrs) do
    wizard
    |> FormWizard.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Delete a wizard and all its steps.
  """
  def delete_wizard(%FormWizard{} = wizard) do
    Repo.delete(wizard)
  end

  @doc """
  Add a step to a wizard.
  """
  def add_wizard_step(wizard_id, form_id, step_number, attrs \\ %{}) do
    step_attrs =
      attrs
      |> Map.put(:wizard_id, wizard_id)
      |> Map.put(:form_id, form_id)
      |> Map.put(:step_number, step_number)

    %FormWizardStep{}
    |> FormWizardStep.changeset(step_attrs)
    |> Repo.insert()
  end

  @doc """
  Get all steps for a wizard, ordered by step number.
  """
  def get_wizard_steps(wizard_id) do
    FormWizardStep
    |> where([s], s.wizard_id == ^wizard_id)
    |> order_by([s], asc: s.step_number)
    |> preload([:form])
    |> Repo.all()
  end

  @doc """
  Update a wizard step.
  """
  def update_wizard_step(%FormWizardStep{} = step, attrs) do
    step
    |> FormWizardStep.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Remove a step from a wizard.
  """
  def remove_wizard_step(%FormWizardStep{} = step) do
    Repo.delete(step)
  end

  @doc """
  Reorder wizard steps.
  """
  def reorder_wizard_steps(wizard_id, step_order) do
    Repo.transaction(fn ->
      step_order
      |> Enum.with_index(1)
      |> Enum.each(fn {step_id, new_position} ->
        step = Repo.get!(FormWizardStep, step_id)
        step
        |> FormWizardStep.changeset(%{step_number: new_position})
        |> Repo.update!()
      end)
    end)
  end

  @doc """
  Get the first step of a wizard.
  """
  def get_first_wizard_step(wizard_id) do
    FormWizardStep
    |> where([s], s.wizard_id == ^wizard_id and s.step_number == 1)
    |> preload([:form])
    |> Repo.one()
  end

  @doc """
  Get the next step in a wizard based on current step and form data.
  """
  def get_next_wizard_step(current_step_id, form_data \\ %{}) do
    case Repo.get(FormWizardStep, current_step_id) do
      nil -> {:error, :step_not_found}
      step ->
        case FormWizardStep.get_next_step(step, form_data) do
          {:ok, next_step_id} ->
            next_step = Repo.get(FormWizardStep, next_step_id)
            {:ok, next_step}
          error -> error
        end
    end
  end

  @doc """
  Check if a wizard step's conditions are met.
  """
  def evaluate_step_conditions(step_id, form_data) do
    case Repo.get(FormWizardStep, step_id) do
      nil -> {:error, :step_not_found}
      step -> {:ok, FormWizardStep.evaluate_conditions(step, form_data)}
    end
  end

  @doc """
  Get wizard progress information.
  """
  def get_wizard_progress(wizard_id, current_step_number) do
    total_steps =
      FormWizardStep
      |> where([s], s.wizard_id == ^wizard_id)
      |> select([s], count(s.id))
      |> Repo.one()

    progress_percentage = if total_steps > 0 do
      round((current_step_number / total_steps) * 100)
    else
      0
    end

    %{
      current_step: current_step_number,
      total_steps: total_steps,
      progress_percentage: progress_percentage,
      is_complete: current_step_number >= total_steps
    }
  end

  @doc """
  Validate wizard step configuration.
  """
  def validate_wizard_step_configuration(wizard_id) do
    steps = get_wizard_steps(wizard_id)

    errors = []

    # Check for missing step numbers
    step_numbers = Enum.map(steps, & &1.step_number)
    expected_numbers = 1..length(steps) |> Enum.to_list()

    errors = if step_numbers != expected_numbers do
      ["Missing or duplicate step numbers" | errors]
    else
      errors
    end

    # Check for orphaned next_step_id references
    all_step_ids = Enum.map(steps, & &1.id)

    orphaned_references =
      steps
      |> Enum.filter(& &1.next_step_id)
      |> Enum.reject(& &1.next_step_id in all_step_ids)

    errors = if length(orphaned_references) > 0 do
      ["Invalid next_step_id references found" | errors]
    else
      errors
    end

    if Enum.empty?(errors) do
      {:ok, :valid}
    else
      {:error, errors}
    end
  end

  # =============================================================================
  # MOBILE FORM VALIDATION FUNCTIONS
  # =============================================================================

  @doc """
  Link a mobile form to a dynamic form for validation.
  """
  def link_mobile_form_to_validation(mobile_form_id, dynamic_form_id) do
    %MobileFormValidationLink{}
    |> MobileFormValidationLink.changeset(%{
      mobile_form_id: mobile_form_id,
      dynamic_form_id: dynamic_form_id
    })
    |> Repo.insert()
  end

  @doc """
  Remove validation link between mobile form and dynamic form.
  """
  def unlink_mobile_form_validation(mobile_form_id, dynamic_form_id) do
    query = from mvl in MobileFormValidationLink,
            where: mvl.mobile_form_id == ^mobile_form_id and mvl.dynamic_form_id == ^dynamic_form_id

    Repo.delete_all(query)
  end

  @doc """
  Get the validation schema for a mobile form.
  """
  def get_validation_schema_for_mobile_form(mobile_form_id) do
    query = from df in DynamicForm,
            join: mvl in MobileFormValidationLink, on: mvl.dynamic_form_id == df.id,
            where: mvl.mobile_form_id == ^mobile_form_id,
            select: df,
            limit: 1

    Repo.one(query)
  end

  @doc """
  Validate mobile form data using linked dynamic form schema.
  """
  def validate_mobile_form_data(mobile_form_id, form_data) do
    case get_validation_schema_for_mobile_form(mobile_form_id) do
      nil ->
        # No validation schema linked, return success
        {:ok, form_data}

      dynamic_form ->
        validate_against_schema(dynamic_form.validation_schema, form_data)
    end
  end

  @doc """
  List mobile forms with their validation links for wizard builder.
  """
  def list_mobile_forms_for_wizard do
    case MobileFormsV2Service.list_forms(%{page_id: nil}) do
      {:ok, forms} ->
        # Preload the page and screen relationships
        forms_with_preloads = Repo.preload(forms, page: :screen)

        forms_with_validation = Enum.map(forms_with_preloads, fn form ->
          validation_schema = get_validation_schema_for_mobile_form(form.id)

          Map.put(form, :validation_schema, validation_schema)
        end)

        {:ok, forms_with_validation}

      error -> error
    end
  end

  @doc """
  Enhanced wizard step creation for mobile forms.
  """
  def add_mobile_wizard_step(wizard_id, mobile_form_id, step_number, attrs \\ %{}) do
    step_attrs =
      attrs
      |> Map.put(:wizard_id, wizard_id)
      |> Map.put(:mobile_form_id, mobile_form_id)
      |> Map.put(:step_number, step_number)
      |> Map.put(:step_type, Map.get(attrs, :step_type, "form"))

    %FormWizardStep{}
    |> FormWizardStep.changeset(step_attrs)
    |> Repo.insert()
  end

  @doc """
  Get wizard steps with mobile form data preloaded.
  """
  def get_wizard_steps_with_forms(wizard_id) do
    FormWizardStep
    |> where([s], s.wizard_id == ^wizard_id)
    |> order_by([s], asc: s.step_number)
    |> preload([:form, :mobile_form])
    |> Repo.all()
  end


  @doc """
  Convert mobile form fields to dynamic form structure for validation.
  """
  def convert_mobile_form_to_dynamic_schema(mobile_form_id) do
    with {:ok, fields} <- MobileFormsV2Service.get_form_fields(mobile_form_id) do
      form_structure = %{
        "fields" => Enum.map(fields, &convert_mobile_field_to_dynamic/1)
      }

      validation_schema = generate_json_schema(%{"form" => form_structure})

      {:ok, %{
        form: form_structure,
        validation_schema: validation_schema
      }}
    end
  end

  # Convert mobile form field to dynamic form field structure
  defp convert_mobile_field_to_dynamic(field) do
    %{
      "name" => field.field_name,
      "type" => map_mobile_field_type(field.field_type),
      "required" => field.is_required,
      "description" => field.label
    }
  end

  # Map mobile field types to JSON schema types
  defp map_mobile_field_type(field_type) do
    case field_type do
      "string" -> "string"
      "number" -> "number"
      "integer" -> "integer"
      "boolean" -> "boolean"
      "date" -> "string"
      "datetime" -> "string"
      "email" -> "string"
      "password" -> "string"
      "phone" -> "string"
      "select" -> "string"
      "multiselect" -> "array"
      "textarea" -> "string"
      _ -> "string"
    end
  end

  # =============================================================================
  # ROUTE-WIZARD LINKING FUNCTIONS
  # =============================================================================

  @doc """
  Link a wizard to a route.
  """
  def link_wizard_to_route(route_id, wizard_id) do
    %DynamicRouteWizard{}
    |> DynamicRouteWizard.changeset(%{route_id: route_id, wizard_id: wizard_id})
    |> Repo.insert()
  end

  @doc """
  Unlink a wizard from a route.
  """
  def unlink_wizard_from_route(route_id, wizard_id) do
    query = from rw in DynamicRouteWizard,
            where: rw.route_id == ^route_id and rw.wizard_id == ^wizard_id

    Repo.delete_all(query)
  end

  @doc """
  Get the wizard linked to a route.
  """
  def get_wizard_for_route(route_id) do
    query = from w in FormWizard,
            join: rw in DynamicRouteWizard, on: rw.wizard_id == w.id,
            where: rw.route_id == ^route_id,
            select: w,
            limit: 1

    Repo.one(query) |> IO.inspect(label: "WIZARD QUERY")
  end

  @doc """
  Get all wizards linked to a route.
  """
  def get_route_wizards(route_id) do
    query = from w in FormWizard,
            join: rw in DynamicRouteWizard, on: rw.wizard_id == w.id,
            where: rw.route_id == ^route_id,
            select: w

    Repo.all(query)
  end

  @doc """
  Check if a route has any wizards linked.
  """
  def route_has_wizards?(route_id) do
    query = from rw in DynamicRouteWizard,
            where: rw.route_id == ^route_id,
            select: count(rw.id)

    Repo.one(query) > 0
  end

  @doc """
  Get routes linked to a specific wizard.
  """
  def get_wizard_routes(wizard_id) do
    query = from r in DynamicRoute,
            join: rw in DynamicRouteWizard, on: rw.route_id == r.id,
            where: rw.wizard_id == ^wizard_id,
            select: r

    Repo.all(query)
  end

  @doc """
  Enhanced wizard linking that replaces existing wizard if present.
  """
  def replace_route_wizard(route_id, wizard_id) do
    Repo.transaction(fn ->
      # Remove any existing wizard links for this route
      from(rw in DynamicRouteWizard, where: rw.route_id == ^route_id)
      |> Repo.delete_all()

      # Add the new wizard link
      case link_wizard_to_route(route_id, wizard_id) do
        {:ok, link} -> link
        {:error, changeset} -> Repo.rollback(changeset)
      end
    end)
  end

  # =============================================================================
  # WIZARD STEP ORDERING AND REORDERING FUNCTIONS
  # =============================================================================

  @doc """
  Reorder wizard steps by providing new step order.
  """
  def reorder_wizard_steps_by_ids(wizard_id, step_ids_in_order) do
    Repo.transaction(fn ->
      # Validate that all step IDs belong to this wizard
      existing_steps = get_wizard_steps_with_forms(wizard_id)
      existing_step_ids = Enum.map(existing_steps, & &1.id)

      if Enum.sort(step_ids_in_order) != Enum.sort(existing_step_ids) do
        Repo.rollback("Invalid step IDs provided")
      else
        # Update step numbers based on new order
        step_ids_in_order
        |> Enum.with_index(1)
        |> Enum.each(fn {step_id, new_step_number} ->
          step = Repo.get!(FormWizardStep, step_id)
          step
          |> FormWizardStep.changeset(%{step_number: new_step_number})
          |> Repo.update!()
        end)

        :ok
      end
    end)
  end

  @doc """
  Move a wizard step up in order (decrease step number).
  """
  def move_wizard_step_up(step_id) do
    Repo.transaction(fn ->
      step = Repo.get!(FormWizardStep, step_id)

      if step.step_number <= 1 do
        Repo.rollback("Cannot move first step up")
      else
        # Find the step that currently has the target position
        target_step = FormWizardStep
        |> where([s], s.wizard_id == ^step.wizard_id and s.step_number == ^(step.step_number - 1))
        |> Repo.one!()

        # Use temporary step numbers to avoid unique constraint violation
        # Get max step number for this wizard and add 1000 as temp offset
        max_step = FormWizardStep
        |> where([s], s.wizard_id == ^step.wizard_id)
        |> select([s], max(s.step_number))
        |> Repo.one() || 0

        temp_offset = max_step + 1000

        # Move current step to temporary position
        step
        |> FormWizardStep.changeset(%{step_number: temp_offset})
        |> Repo.update!()

        # Move target step to current step's position
        target_step
        |> FormWizardStep.changeset(%{step_number: step.step_number})
        |> Repo.update!()

        # Move current step to target position
        step
        |> FormWizardStep.changeset(%{step_number: target_step.step_number})
        |> Repo.update!()

        :ok
      end
    end)
  end

  @doc """
  Move a wizard step down in order (increase step number).
  """
  def move_wizard_step_down(step_id) do
    Repo.transaction(fn ->
      step = Repo.get!(FormWizardStep, step_id)

      # Get total steps for this wizard
      total_steps = FormWizardStep
      |> where([s], s.wizard_id == ^step.wizard_id)
      |> select([s], count(s.id))
      |> Repo.one()

      if step.step_number >= total_steps do
        Repo.rollback("Cannot move last step down")
      else
        # Find the step that currently has the target position
        target_step = FormWizardStep
        |> where([s], s.wizard_id == ^step.wizard_id and s.step_number == ^(step.step_number + 1))
        |> Repo.one!()

        # Use temporary step numbers to avoid unique constraint violation
        # Get max step number for this wizard and add 1000 as temp offset
        max_step = FormWizardStep
        |> where([s], s.wizard_id == ^step.wizard_id)
        |> select([s], max(s.step_number))
        |> Repo.one() || 0

        temp_offset = max_step + 1000

        # Move current step to temporary position
        step
        |> FormWizardStep.changeset(%{step_number: temp_offset})
        |> Repo.update!()

        # Move target step to current step's position
        target_step
        |> FormWizardStep.changeset(%{step_number: step.step_number})
        |> Repo.update!()

        # Move current step to target position
        step
        |> FormWizardStep.changeset(%{step_number: target_step.step_number})
        |> Repo.update!()

        :ok
      end
    end)
  end

  @doc """
  Insert a new step at a specific position, shifting other steps as needed.
  """
  def insert_wizard_step_at_position(wizard_id, mobile_form_id, target_position, attrs \\ %{}) do
    Repo.transaction(fn ->
      # Get total steps for validation
      total_steps = FormWizardStep
      |> where([s], s.wizard_id == ^wizard_id)
      |> select([s], count(s.id))
      |> Repo.one()

      if target_position < 1 or target_position > total_steps + 1 do
        Repo.rollback("Invalid target position")
      else
        # Shift existing steps at or after target position
        FormWizardStep
        |> where([s], s.wizard_id == ^wizard_id and s.step_number >= ^target_position)
        |> Repo.update_all(inc: [step_number: 1])

        # Insert new step at target position
        step_attrs =
          attrs
          |> Map.put(:wizard_id, wizard_id)
          |> Map.put(:mobile_form_id, mobile_form_id)
          |> Map.put(:step_number, target_position)
          |> Map.put(:step_type, Map.get(attrs, :step_type, "form"))

        %FormWizardStep{}
        |> FormWizardStep.changeset(step_attrs)
        |> Repo.insert!()
      end
    end)
  end

  @doc """
  Remove a wizard step and adjust remaining step numbers.
  """
  def remove_wizard_step_and_reorder(step_id) do
    Repo.transaction(fn ->
      step = Repo.get!(FormWizardStep, step_id)

      # Delete the step
      Repo.delete!(step)

      # Shift all steps after this one down by 1
      FormWizardStep
      |> where([s], s.wizard_id == ^step.wizard_id and s.step_number > ^step.step_number)
      |> Repo.update_all(inc: [step_number: -1])

      :ok
    end)
  end

  @doc """
  Get wizard steps ordered by step number with detailed information.
  """
  def get_wizard_steps_ordered(wizard_id) do
    # Get steps with preloaded forms
    steps = FormWizardStep
    |> where([s], s.wizard_id == ^wizard_id)
    |> order_by([s], asc: s.step_number)
    |> preload([:form, :mobile_form, :data_mappings])
    |> Repo.all()

    # Get all mobile form IDs that need form structure data
    mobile_form_ids = steps 
    |> Enum.map(& &1.mobile_form_id) 
    |> Enum.filter(& &1)
    |> Enum.uniq()

    # Batch fetch all mobile form structures to avoid N+1
    mobile_forms_data = case mobile_form_ids do
      [] -> %{}
      ids ->
        # Batch fetch forms
        forms = FormV2Schema
        |> where([f], f.id in ^ids)
        |> Repo.all()
        |> Map.new(&{&1.id, &1})

        # Batch fetch field counts
        field_counts = FormFieldV2Schema
        |> where([ff], ff.form_id in ^ids and ff.active == true)
        |> group_by([ff], ff.form_id)
        |> select([ff], {ff.form_id, count(ff.id)})
        |> Repo.all()
        |> Map.new()

        # Combine form data
        Enum.reduce(forms, %{}, fn {form_id, form}, acc ->
          Map.put(acc, form_id, %{
            name: form.name,
            fields_count: Map.get(field_counts, form_id, 0)
          })
        end)
    end

    # Map steps with preloaded form info
    Enum.map(steps, fn step ->
      form_info = cond do
        step.mobile_form_id ->
          case Map.get(mobile_forms_data, step.mobile_form_id) do
            %{name: name, fields_count: count} ->
              %{
                type: "mobile_form",
                id: step.mobile_form_id,
                name: name,
                fields_count: count
              }
            _ ->
              %{type: "mobile_form", id: step.mobile_form_id, name: "Unknown", fields_count: 0}
          end

        step.form_id and step.form ->
          %{
            type: "dynamic_form",
            id: step.form_id,
            name: step.form.name,
            fields_count: length(step.form.form["fields"] || [])
          }

        true ->
          %{type: "unknown", id: nil, name: "No form", fields_count: 0}
      end

      Map.put(step, :form_info, form_info)
    end)
  end

  # Helper function to get mobile form structure (moved from controller)
  defp get_mobile_form_structure(mobile_form_id) do
    with {:ok, form} <- MobileFormsV2Service.get_form(%{"form_id" => mobile_form_id}),
         {:ok, fields} <- MobileFormsV2Service.get_form_fields(mobile_form_id) do

      form_structure = %{
        id: form.id,
        name: form.name,
        submit_to: form.submit_to,
        fields: Enum.map(fields, fn field ->
          %{
            name: field.field_name,
            type: field.field_type,
            label: field.label,
            required: field.is_required,
            order: field.field_order,
            options: field.options
          }
        end)
      }

      {:ok, form_structure}
    end
  end

  # =============================================================================
  # WIZARD SESSION MANAGEMENT FUNCTIONS
  # =============================================================================

  @doc """
  Create a new wizard session.
  """
  def create_wizard_session(wizard_id, client_info \\ %{}) do
    with {:ok, wizard} <- get_wizard_with_first_step(wizard_id),
         {:ok, first_step} <- get_first_step(wizard) do

      session_attrs = %{
        session_token: WizardSession.generate_session_token(),
        wizard_id: wizard_id,
        current_step_number: 1,
        current_step_id: first_step.id,
        form_data: %{},
        completed_steps: [],
        status: "active",
        client_ip: Map.get(client_info, :client_ip),
        user_agent: Map.get(client_info, :user_agent),
        expires_at: WizardSession.default_expires_at()
      }

      %WizardSession{}
      |> WizardSession.changeset(session_attrs)
      |> Repo.insert()
    else
      {:error, :wizard_not_found} -> {:error, "Wizard not found"}
      {:error, :no_steps} -> {:error, "Wizard has no steps configured"}
      error -> error
    end
  end

  @doc """
  Get wizard session by token.
  """
  def get_wizard_session(session_token) do
    case Repo.get_by(WizardSession, session_token: session_token) do
      nil -> {:error, :session_not_found}
      session ->
        if WizardSession.active?(session) do
          session = Repo.preload(session, [:wizard, :current_step])
          {:ok, session}
        else
          {:error, :session_expired}
        end
    end
  end

  @doc """
  Update wizard session with new step data.
  """
  def update_wizard_session(session_token, step_data, next_step_number \\ nil) do
    with {:ok, session} <- get_wizard_session(session_token),
         {:ok, updated_session} <- update_session_data(session, step_data, next_step_number) do
      {:ok, updated_session}
    end
  end

  @doc """
  Complete wizard session.
  """
  def complete_wizard_session(session_token) do
    with {:ok, session} <- get_wizard_session(session_token) do
      session
      |> WizardSession.changeset(%{status: "completed"})
      |> Repo.update()
    end
  end

  @doc """
  Get wizard session with detailed step information.
  """
  def get_wizard_session_with_details(session_token) do
    with {:ok, session} <- get_wizard_session(session_token) do
      # Load current step with form details
      current_step = get_step_with_form(session.current_step_id)

      # Get all wizard steps for navigation
      all_steps = get_wizard_steps_with_forms(session.wizard_id)
      total_steps = length(all_steps)

      # Determine next step
      next_step = get_next_step_in_sequence(all_steps, session.current_step_number)

      session_details = %{
        session: session,
        current_step: current_step,
        next_step: next_step,
        total_steps: total_steps,
        is_final_step: session.current_step_number >= total_steps,
        progress_percentage: round((session.current_step_number / total_steps) * 100)
      }

      {:ok, session_details}
    end
  end

  @doc """
  Clean up expired wizard sessions.
  """
  def cleanup_expired_sessions do
    now = DateTime.utc_now()

    from(ws in WizardSession,
         where: ws.expires_at < ^now or ws.status == "expired")
    |> Repo.delete_all()
  end

  # Private helper functions for wizard session management

  defp get_wizard_with_first_step(wizard_id) do
    case get_wizard(wizard_id) do
      nil -> {:error, :wizard_not_found}
      wizard -> {:ok, wizard}
    end
  end

  defp get_first_step(wizard) do
    case get_first_wizard_step(wizard.id) do
      nil -> {:error, :no_steps}
      step -> {:ok, step}
    end
  end

  defp update_session_data(session, step_data, next_step_number) do
    current_step_key = "step_#{session.current_step_number}"
    updated_form_data = Map.put(session.form_data, current_step_key, step_data)
    updated_completed_steps = [session.current_step_number | session.completed_steps] |> Enum.uniq()

    update_attrs = %{
      form_data: updated_form_data,
      completed_steps: updated_completed_steps
    }

    update_attrs = if next_step_number do
      next_step = get_step_by_wizard_and_number(session.wizard_id, next_step_number)
      Map.merge(update_attrs, %{
        current_step_number: next_step_number,
        current_step_id: next_step && next_step.id
      })
    else
      update_attrs
    end

    session
    |> WizardSession.changeset(update_attrs)
    |> Repo.update()
  end

  defp get_step_with_form(step_id) do
    FormWizardStep
    |> where([s], s.id == ^step_id)
    |> preload([:form])
    |> Repo.one()
  end

  defp get_step_by_wizard_and_number(wizard_id, step_number) do
    FormWizardStep
    |> where([s], s.wizard_id == ^wizard_id and s.step_number == ^step_number)
    |> Repo.one()
  end

  defp get_next_step_in_sequence(steps, current_step_number) do
    Enum.find(steps, &(&1.step_number == current_step_number + 1))
  end

  # =============================================================================
  # STEP DATA MAPPING FUNCTIONS
  # =============================================================================

  @doc """
  Create a data mapping for a wizard step field
  """
  def create_step_data_mapping(attrs) do
    %FormWizardStepDataMapping{}
    |> FormWizardStepDataMapping.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Update a step data mapping
  """
  def update_step_data_mapping(%FormWizardStepDataMapping{} = mapping, attrs) do
    mapping
    |> FormWizardStepDataMapping.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Delete a step data mapping
  """
  def delete_step_data_mapping(%FormWizardStepDataMapping{} = mapping) do
    Repo.delete(mapping)
  end

  @doc """
  Get data mappings for a wizard step with preloaded associations
  """
  def get_step_data_mappings(wizard_step_id) do
    FormWizardStepDataMapping
    |> where([m], m.wizard_step_id == ^wizard_step_id and m.active == true)
    |> preload([:target_field])
    |> order_by([m], [asc: m.source_step_number, asc: m.inserted_at])
    |> Repo.all()
  end

  @doc """
  Get a step data mapping by ID
  """
  def get_step_data_mapping!(id) do
    FormWizardStepDataMapping
    |> preload([:wizard_step, :target_field])
    |> Repo.get!(id)
  end

  @doc """
  Get available fields for a wizard step to map data to
  """
  def get_available_fields_for_mapping(wizard_step_id) do
    case get_wizard_step_with_form(wizard_step_id) do
      %FormWizardStep{mobile_form_id: mobile_form_id} when not is_nil(mobile_form_id) ->
        case MobileFormsV2Service.get_form_fields(mobile_form_id) do
          {:ok, fields} -> {:ok, fields}
          {:error, reason} -> {:error, reason}
        end
      _ -> {:error, "Step not found or no mobile form associated"}
    end
  end

  @doc """
  Get wizard steps that can be used as source for data mapping (previous steps)
  """
  def get_available_source_steps(wizard_id, current_step_number) do
    FormWizardStep
    |> where([s], s.wizard_id == ^wizard_id and s.step_number < ^current_step_number)
    |> preload([:mobile_form])
    |> order_by([s], asc: s.step_number)
    |> Repo.all()
    |> Enum.map(fn step ->
      %{
        id: step.id,
        step_number: step.step_number,
        form_name: step.mobile_form && step.mobile_form.name || "Unknown Form"
      }
    end)
  end

  @doc """
  Get wizard step with mobile form preloaded
  """
  def get_wizard_step_with_form(step_id) do
    FormWizardStep
    |> where([s], s.id == ^step_id)
    |> preload([:mobile_form, :data_mappings])
    |> Repo.one()
  end

  @doc """
  Apply data mappings to populate field values for a wizard step
  """
  def apply_data_mappings_to_fields(wizard_step_id, current_step_number, wizard_id) do
    mappings = get_step_data_mappings(wizard_step_id)
    
    Enum.reduce(mappings, %{}, fn mapping, acc ->
      if FormWizardStepDataMapping.should_execute_mapping?(mapping, current_step_number) do
        case get_wizard_step_data(wizard_id, mapping.source_step_number) do
          {:ok, source_data} ->
            case FormWizardStepDataMapping.extract_value(source_data, mapping.source_json_path) do
              {:ok, value} ->
                Map.put(acc, mapping.target_field.field_name, value)
              {:error, _reason} ->
                acc
            end
          {:error, _reason} ->
            acc
        end
      else
        acc
      end
    end)
  end

  # Helper function to get wizard step data from Process dictionary
  defp get_wizard_step_data(wizard_id, step_number) do
    case Process.get("wizard_#{wizard_id}_step_#{step_number}") do
      nil -> {:error, "No data found for step #{step_number}"}
      data -> {:ok, data}
    end
  end
end
