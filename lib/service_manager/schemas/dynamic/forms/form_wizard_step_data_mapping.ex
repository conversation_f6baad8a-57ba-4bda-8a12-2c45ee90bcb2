defmodule ServiceManager.Forms.FormWizardStepDataMapping do
  use Ecto.Schema
  import Ecto.Changeset
  alias ServiceManager.Forms.{FormWizardStep}
  alias ServiceManagerWeb.Api.FormFieldV2Schema

  @derive {Jason.Encoder, only: [:id, :wizard_step_id, :target_field_id, :source_step_number, :source_json_path, :active]}
  schema "form_wizard_step_data_mappings" do
    field :source_step_number, :integer
    field :source_json_path, :string
    field :active, :boolean, default: true
    
    belongs_to :wizard_step, FormWizardStep, foreign_key: :wizard_step_id
    belongs_to :target_field, FormFieldV2Schema, type: Ecto.UUID, foreign_key: :target_field_id

    timestamps()
  end

  @doc false
  def changeset(mapping, attrs) do
    mapping
    |> cast(attrs, [:wizard_step_id, :target_field_id, :source_step_number, :source_json_path, :active])
    |> validate_required([:wizard_step_id, :target_field_id, :source_step_number, :source_json_path])
    |> validate_number(:source_step_number, greater_than: 0)
    |> validate_length(:source_json_path, min: 1, max: 500)
    |> validate_json_path()
    |> foreign_key_constraint(:wizard_step_id)
    |> foreign_key_constraint(:target_field_id)
    |> unique_constraint([:wizard_step_id, :target_field_id], 
         message: "target field can only have one data mapping per wizard step")
  end

  defp validate_json_path(changeset) do
    case get_field(changeset, :source_json_path) do
      nil -> changeset
      path when is_binary(path) ->
        if valid_json_path?(path) do
          changeset
        else
          add_error(changeset, :source_json_path, "must be a valid JSON path pattern (e.g., 'field_name', 'nested.field', 'array[0].field')")
        end
      _ -> add_error(changeset, :source_json_path, "must be a string")
    end
  end

  # Basic JSON path validation - supports dot notation and array indexing
  defp valid_json_path?(path) do
    # Allow alphanumeric, underscores, dots, brackets, and array indices
    Regex.match?(~r/^[a-zA-Z_][a-zA-Z0-9_]*(\.[a-zA-Z_][a-zA-Z0-9_]*|\[\d+\])*$/, path)
  end

  @doc """
  Extract value from source data using JSON path
  """
  def extract_value(source_data, json_path) when is_map(source_data) and is_binary(json_path) do
    try do
      path_parts = parse_json_path(json_path)
      extract_nested_value(source_data, path_parts)
    rescue
      _ -> {:error, "Invalid JSON path or data structure"}
    end
  end

  def extract_value(_, _), do: {:error, "Source data must be a map and JSON path must be a string"}

  # Parse JSON path into list of keys and array indices
  defp parse_json_path(path) do
    path
    |> String.split(".")
    |> Enum.flat_map(fn part ->
      case Regex.run(~r/^([^[]+)(\[(\d+)\])?$/, part) do
        [_, key, "", ""] -> [key]
        [_, key, _, index] -> [key, String.to_integer(index)]
        _ -> [part]
      end
    end)
  end

  # Extract nested value using parsed path parts
  defp extract_nested_value(data, []), do: {:ok, data}
  defp extract_nested_value(data, [key | rest]) when is_map(data) do
    case Map.get(data, key) || Map.get(data, to_string(key)) do
      nil -> {:error, "Key '#{key}' not found in data"}
      value -> extract_nested_value(value, rest)
    end
  end
  defp extract_nested_value(data, [index | rest]) when is_list(data) and is_integer(index) do
    case Enum.at(data, index) do
      nil -> {:error, "Index #{index} not found in array"}
      value -> extract_nested_value(value, rest)
    end
  end
  defp extract_nested_value(_, [key | _]) do
    {:error, "Cannot access key '#{key}' on non-map data"}
  end

  @doc """
  Check if mapping should be executed based on source step completion
  """
  def should_execute_mapping?(%__MODULE__{source_step_number: source_step}, current_step_number) 
      when source_step < current_step_number, do: true
  def should_execute_mapping?(_, _), do: false
end