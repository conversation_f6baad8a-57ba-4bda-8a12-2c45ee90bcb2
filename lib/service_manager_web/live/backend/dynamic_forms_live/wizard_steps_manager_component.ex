defmodule ServiceManagerWeb.Backend.DynamicFormsLive.WizardStepsManagerComponent do
  use ServiceManagerWeb, :live_component
  alias ServiceManager.Forms.DynamicFormsManager
  alias ServiceManager.Triggers.{TriggerContext, TriggerManager}

  @impl true
  def update(assigns, socket) do
    wizard = assigns.wizard
    
    {:ok,
     socket
     |> assign(assigns)
     |> assign(:show_reorder_modal, false)
     |> assign(:show_add_form_modal, false)
     |> assign(:form_search_query, "")
     |> assign(:show_triggers_section, false)
     |> assign(:show_trigger_form, false)
     |> assign(:selected_trigger, nil)
     |> assign(:trigger_action, :new)
     |> assign(:current_tab, :steps)
     |> assign(:show_data_mappings_modal, false)
     |> assign(:selected_step_for_mapping, nil)
     |> assign(:step_data_mappings, [])
     |> assign(:available_target_fields, [])
     |> assign(:available_source_steps, [])
     |> assign(:show_mapping_form, false)
     |> assign(:selected_mapping, nil)
     |> assign(:mapping_action, :new)
     # Load data asynchronously
     |> assign_async(:ordered_steps, fn ->
       try do
         steps = DynamicFormsManager.get_wizard_steps_ordered(wizard.id)
         {:ok, steps || []}
       rescue
         _ -> {:ok, []}
       end
     end)
     |> assign_async(:available_mobile_forms, fn -> 
       case DynamicFormsManager.list_mobile_forms_for_wizard() do
         {:ok, forms} -> {:ok, forms}
         {:error, _} -> {:ok, []}
       end
     end)
     |> assign_async(:wizard_triggers, fn -> 
       {:ok, TriggerContext.list_wizard_triggers(wizard.id)}
     end)
     |> assign_async(:available_triggers, fn -> 
       {:ok, TriggerManager.list_triggers(enabled: true)}
     end)
     |> assign_async(:available_templates, fn -> 
       {:ok, TriggerContext.list_available_templates()}
     end)
     |> assign(:filtered_available_forms, [])}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <!-- Tab Navigation -->
      <div class="border-b border-gray-200 mb-6">
        <nav class="-mb-px flex space-x-8">
          <button
            phx-click="switch_tab"
            phx-value-tab="steps"
            phx-target={@myself}
            class={[
              "py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors duration-200",
              if(@current_tab == :steps, do: "border-indigo-500 text-indigo-600", else: "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300")
            ]}
          >
            <.icon name="hero-bars-3" class="h-4 w-4 inline mr-2" />
            Wizard Steps
            <span class="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs font-medium">
              <%= case @ordered_steps do %>
                <% %{loading: true} -> %>
                  <div class="animate-spin h-3 w-3 border border-gray-300 border-t-gray-600 rounded-full inline-block"></div>
                <% %{result: steps} when is_list(steps) -> %>
                  <%= length(steps) %>
                <% _ -> %>
                  0
              <% end %>
            </span>
          </button>

          <button
            phx-click="switch_tab"
            phx-value-tab="triggers"
            phx-target={@myself}
            class={[
              "py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors duration-200",
              if(@current_tab == :triggers, do: "border-indigo-500 text-indigo-600", else: "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300")
            ]}
          >
            <.icon name="hero-bolt" class="h-4 w-4 inline mr-2" />
            Triggers
            <span class="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs font-medium">
              <%= case @wizard_triggers do %>
                <% %{loading: true} -> %>
                  <div class="animate-spin h-3 w-3 border border-gray-300 border-t-gray-600 rounded-full inline-block"></div>
                <% %{result: triggers} when is_list(triggers) -> %>
                  <%= length(triggers) %>
                <% _ -> %>
                  0
              <% end %>
            </span>
          </button>

          <button
            phx-click="switch_tab"
            phx-value-tab="data_mappings"
            phx-target={@myself}
            class={[
              "py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors duration-200",
              if(@current_tab == :data_mappings, do: "border-indigo-500 text-indigo-600", else: "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300")
            ]}
          >
            <.icon name="hero-arrow-path" class="h-4 w-4 inline mr-2" />
            Data Mappings
            <span class="ml-2 bg-gray-100 text-gray-900 py-0.5 px-2.5 rounded-full text-xs font-medium">
              <%= case @ordered_steps do %>
                <% %{loading: true} -> %>
                  <div class="animate-spin h-3 w-3 border border-gray-300 border-t-gray-600 rounded-full inline-block"></div>
                <% %{result: steps} when is_list(steps) -> %>
                  <%= Enum.count(steps, fn step -> length(Map.get(step, :data_mappings, [])) > 0 end) %>
                <% _ -> %>
                  0
              <% end %>
            </span>
          </button>
        </nav>
      </div>

      <%= if @current_tab == :triggers do %>
        <!-- Triggers Section -->
        <div class="space-y-6">
          <div class="flex items-center justify-between">
            <div>
              <h4 class="text-sm font-medium text-gray-900">Wizard Triggers</h4>
              <p class="text-xs text-gray-500 mt-1">Execute triggers at different points in the wizard flow</p>
            </div>
            <%= case @available_triggers do %>
              <% %{loading: true} -> %>
                <div class="animate-pulse bg-gray-200 h-10 w-24 rounded-md"></div>
              <% %{result: _} -> %>
                <button
                  phx-click="show_trigger_form"
                  phx-target={@myself}
                  class="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 transition-colors"
                >
                  <.icon name="hero-plus" class="h-4 w-4 mr-2" />
                  Add Trigger
                </button>
              <% _ -> %>
                <div class="animate-pulse bg-gray-200 h-10 w-24 rounded-md"></div>
            <% end %>
          </div>

          <!-- Trigger Form -->
          <%= if @show_trigger_form do %>
            <div class="bg-white border border-gray-200 rounded-lg p-6 mb-6">
              <div class="flex items-center justify-between mb-4">
                <h5 class="text-sm font-medium text-gray-900">
                  <%= if @trigger_action == :edit, do: "Edit", else: "Add" %> Wizard Trigger
                </h5>
                <button
                  phx-click="hide_trigger_form"
                  phx-target={@myself}
                  class="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <.icon name="hero-x-mark" class="h-5 w-5" />
                </button>
              </div>

              <.simple_form
                for={%{}}
                as={:wizard_trigger}
                phx-submit="save_wizard_trigger"
                phx-target={@myself}
                phx-change="validate_trigger_form"
                phx-target={@myself}
                class="space-y-4"
              >
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <!-- Trigger Selection -->
                  <.input
                    name="wizard_trigger[trigger_id]"
                    type="select"
                    label="Trigger"
                    options={[{"Select a trigger...", ""}] ++ Enum.map(@available_triggers, &{"#{&1.name} (#{&1.module_name}.#{&1.function_name})", &1.id})}
                    value={@selected_trigger && @selected_trigger.trigger_id || ""}
                    required
                  />

                  <!-- Execution Timing -->
                  <.input
                    name="wizard_trigger[execution_timing]"
                    type="select"
                    label="Execution Timing"
                    options={[{"Select timing...", ""}] ++ [
                      {"Form Entry - When wizard starts", "form_entry"},
                      {"Form Exit - When wizard completes", "form_exit"},
                      {"Step Entry - When entering a step", "step_entry"},
                      {"Step Exit - When exiting a step", "step_exit"}
                    ]}
                    value={@selected_trigger && @selected_trigger.execution_timing || ""}
                    required
                  />
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <!-- Wizard Step (for step-level triggers) -->
                  <.input
                    name="wizard_trigger[wizard_step_id]"
                    type="select"
                    label="Wizard Step (Optional)"
                    options={[{"All steps / Form level", ""}] ++ (case @ordered_steps do
                      %{result: steps} when is_list(steps) -> Enum.map(steps, &{"Step #{&1.step_number}: #{&1.form_info.name}", &1.id})
                      _ -> []
                    end)}
                    value={@selected_trigger && @selected_trigger.wizard_step_id || ""}
                    help="Leave blank for form-level triggers"
                  />

                  <!-- Execution Order -->
                  <.input
                    name="wizard_trigger[execution_order]"
                    type="select"
                    label="Execution Order"
                    options={Enum.map(1..10, &{to_string(&1), &1})}
                    value={@selected_trigger && @selected_trigger.execution_order || 1}
                    required
                  />
                </div>

                <!-- Request Template -->
                <.input
                  name="wizard_trigger[request_template_id]"
                  type="select"
                  label="Request Template (Optional)"
                  options={[{"No template", ""}] ++ Enum.map(@available_templates, &{&1.name, &1.id})}
                  value={@selected_trigger && @selected_trigger.request_template_id || ""}
                />

                <!-- Data Mapping -->
                <div>
                  <div class="flex items-center justify-between mb-2">
                    <label class="block text-sm font-medium text-gray-700">Data Mapping (JSON)</label>
                    <button
                      type="button"
                      phx-click="generate_sample_mapping"
                      phx-target={@myself}
                      class="text-xs text-indigo-600 hover:text-indigo-500"
                    >
                      Generate Sample
                    </button>
                  </div>
                  <.input
                    name="wizard_trigger[data_mapping]"
                    type="textarea"
                    rows="4"
                    placeholder='{"target_field": "source_field", "api_param": "form_data.field_name"}'
                    value={if @selected_trigger && @selected_trigger.data_mapping, do: Jason.encode!(@selected_trigger.data_mapping, pretty: true), else: ""}
                    help="Define how wizard data maps to trigger input"
                  />
                </div>

                <!-- Conditions -->
                <.input
                  name="wizard_trigger[conditions]"
                  type="textarea"
                  label="Conditions (JSON, Optional)"
                  rows="3"
                  placeholder='{"field_name": {"operator": "equals", "value": "expected_value"}}'
                  value={if @selected_trigger && @selected_trigger.conditions, do: Jason.encode!(@selected_trigger.conditions, pretty: true), else: ""}
                  help="Define conditions for trigger execution"
                />

                <!-- Enabled -->
                <.input
                  name="wizard_trigger[enabled]"
                  type="checkbox"
                  label="Enabled"
                  checked={!@selected_trigger || @selected_trigger.enabled}
                />

                <input type="hidden" name="wizard_trigger[wizard_id]" value={@wizard.id} />
                <%= if @selected_trigger do %>
                  <input type="hidden" name="wizard_trigger[id]" value={@selected_trigger.id} />
                <% end %>

                <:actions>
                  <.button
                    type="button"
                    phx-click="hide_trigger_form"
                    phx-target={@myself}
                    class="px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors"
                  >
                    Cancel
                  </.button>
                  <.button type="submit" phx-disable-with="Saving...">
                    <%= if @trigger_action == :edit, do: "Update", else: "Add" %> Trigger
                  </.button>
                </:actions>
              </.simple_form>
            </div>
          <% end %>

          <!-- Triggers by Timing -->
          <%= for timing <- ["form_entry", "form_exit", "step_entry", "step_exit"] do %>
            <div class="bg-gray-50 rounded-lg p-4">
              <h5 class="text-sm font-medium text-gray-700 mb-3">
                <%= case timing do %>
                  <% "form_entry" -> %>
                    <.icon name="hero-play" class="h-4 w-4 inline mr-2 text-green-600" />
                    Form Entry - When wizard starts
                  <% "form_exit" -> %>
                    <.icon name="hero-stop" class="h-4 w-4 inline mr-2 text-red-600" />
                    Form Exit - When wizard completes
                  <% "step_entry" -> %>
                    <.icon name="hero-arrow-right" class="h-4 w-4 inline mr-2 text-blue-600" />
                    Step Entry - When entering specific steps
                  <% "step_exit" -> %>
                    <.icon name="hero-arrow-left" class="h-4 w-4 inline mr-2 text-orange-600" />
                    Step Exit - When exiting specific steps
                <% end %>
              </h5>

              <%= case @wizard_triggers do %>
                <% %{loading: true} -> %>
                  <div class="animate-pulse space-y-2">
                    <div class="h-4 bg-gray-200 rounded w-3/4"></div>
                    <div class="h-4 bg-gray-200 rounded w-1/2"></div>
                  </div>
                <% %{result: wizard_triggers} -> %>
                  <% timing_triggers = Enum.filter(wizard_triggers, &(&1.execution_timing == timing)) %>
                  <%= if Enum.empty?(timing_triggers) do %>
                    <p class="text-xs text-gray-500 italic">No triggers configured for this timing</p>
                  <% else %>
                    <div class="space-y-2">
                      <%= for trigger <- timing_triggers do %>
                    <div class="flex items-center justify-between p-3 bg-white border border-gray-200 rounded-md">
                      <div class="flex items-center space-x-3">
                        <div class="flex-shrink-0">
                          <.icon name="hero-bolt" class={[
                            "h-4 w-4",
                            if(trigger.enabled, do: "text-green-600", else: "text-gray-400")
                          ]} />
                        </div>
                        <div class="flex-1 min-w-0">
                          <div class="flex items-center space-x-2">
                            <p class="text-sm font-medium text-gray-900"><%= trigger.trigger.name %></p>
                            <span class="text-xs text-gray-500">Order: <%= trigger.execution_order %></span>
                            <%= if trigger.wizard_step do %>
                              <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                Step <%= trigger.wizard_step.step_number %>
                              </span>
                            <% end %>
                          </div>
                          <div class="flex items-center space-x-2 mt-1">
                            <%= if trigger.request_template do %>
                              <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                Template: <%= trigger.request_template.name %>
                              </span>
                            <% end %>
                            <%= if not trigger.enabled do %>
                              <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                                Disabled
                              </span>
                            <% end %>
                          </div>
                        </div>
                      </div>
                      <div class="flex items-center space-x-2">
                        <button
                          phx-click="edit_wizard_trigger"
                          phx-value-id={trigger.id}
                          phx-target={@myself}
                          class="text-xs text-indigo-600 hover:text-indigo-500"
                        >
                          Edit
                        </button>
                        <button
                          phx-click="delete_wizard_trigger"
                          phx-value-id={trigger.id}
                          phx-target={@myself}
                          data-confirm="Are you sure you want to remove this trigger?"
                          class="text-xs text-red-600 hover:text-red-500"
                        >
                          Remove
                        </button>
                      </div>
                    </div>
                      <% end %>
                    </div>
                  <% end %>
                <% _ -> %>
                  <p class="text-xs text-gray-500 italic">Loading triggers...</p>
              <% end %>
            </div>
          <% end %>
        </div>
      <% end %>

      <%= if @current_tab == :data_mappings do %>
        <!-- Data Mappings Section -->
        <div class="space-y-6">
          <div class="flex items-center justify-between">
            <div>
              <h4 class="text-sm font-medium text-gray-900">Step Data Mappings</h4>
              <p class="text-xs text-gray-500 mt-1">Map data from previous wizard steps to fields in current steps</p>
            </div>
          </div>

          <!-- Data Mappings by Step -->
          <%= case @ordered_steps do %>
            <% %{loading: true} -> %>
              <div class="space-y-6">
                <%= for _ <- 1..3 do %>
                  <div class="bg-gray-50 rounded-lg p-4 animate-pulse">
                    <div class="flex items-center justify-between mb-3">
                      <div class="flex items-center space-x-3">
                        <div class="w-6 h-6 bg-gray-200 rounded-full"></div>
                        <div>
                          <div class="h-4 bg-gray-200 rounded w-32 mb-1"></div>
                          <div class="h-3 bg-gray-200 rounded w-20"></div>
                        </div>
                      </div>
                      <div class="w-24 h-10 bg-gray-200 rounded-md"></div>
                    </div>
                    <div class="h-4 bg-gray-200 rounded w-48"></div>
                  </div>
                <% end %>
              </div>
            <% %{result: ordered_steps} -> %>
              <%= for step <- ordered_steps do %>
            <div class="bg-gray-50 rounded-lg p-4">
              <div class="flex items-center justify-between mb-3">
                <div class="flex items-center space-x-3">
                  <div class="flex-shrink-0 w-6 h-6 bg-indigo-100 rounded-full flex items-center justify-center">
                    <span class="text-xs font-medium text-indigo-600"><%= step.step_number %></span>
                  </div>
                  <div>
                    <h5 class="text-sm font-medium text-gray-700">
                      Step <%= step.step_number %>: <%= step.form_info.name %>
                    </h5>
                    <p class="text-xs text-gray-500"><%= step.form_info.fields_count %> fields available</p>
                  </div>
                </div>
                <button
                  phx-click="manage_step_data_mappings"
                  phx-value-step_id={step.id}
                  phx-target={@myself}
                  class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors"
                >
                  <.icon name="hero-cog-6-tooth" class="h-4 w-4 mr-2" />
                  Manage Mappings
                </button>
              </div>

              <!-- Show existing mappings count -->
              <% mapping_count = length(Map.get(step, :data_mappings, [])) %>
              <%= if mapping_count > 0 do %>
                <div class="text-xs text-green-600 font-medium">
                  <.icon name="hero-check-circle" class="h-4 w-4 inline mr-1" />
                  <%= mapping_count %> data mapping(s) configured
                </div>
              <% else %>
                <div class="text-xs text-gray-500 italic">
                  No data mappings configured
                </div>
              <% end %>
            </div>
              <% end %>
            <% _ -> %>
              <div class="space-y-6">
                <div class="text-center">
                  <p class="text-gray-500">Loading wizard steps...</p>
                </div>
              </div>
          <% end %>
        </div>
      <% end %>

      <%= if @current_tab == :steps do %>
        <!-- Steps Section -->
        <div class="flex items-center justify-between mb-4">
          <h4 class="text-sm font-medium text-gray-900">Wizard Steps</h4>
          <div class="flex items-center space-x-2">
            <button
              phx-click="show_add_form_modal"
              phx-target={@myself}
              class="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 transition-colors"
            >
              <.icon name="hero-plus" class="h-4 w-4 mr-2" />
              Add Form
            </button>
            <button
              phx-click="show_reorder_modal"
              phx-target={@myself}
              class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors"
            >
              <.icon name="hero-bars-3" class="h-4 w-4 mr-2" />
              Reorder Steps
            </button>
          </div>
        </div>
      <% end %>

      <!-- Steps List -->
      <div class="space-y-3">
        <%= case @ordered_steps do %>
          <% %{loading: true} -> %>
            <%= for _ <- 1..3 do %>
              <div class="flex items-center justify-between p-4 bg-white border border-gray-200 rounded-lg animate-pulse">
                <div class="flex items-center space-x-3">
                  <div class="w-8 h-8 bg-gray-200 rounded-full"></div>
                  <div>
                    <div class="h-4 bg-gray-200 rounded w-24 mb-2"></div>
                    <div class="h-3 bg-gray-200 rounded w-16"></div>
                  </div>
                </div>
                <div class="w-20 h-8 bg-gray-200 rounded"></div>
              </div>
            <% end %>
          <% %{result: ordered_steps} when is_list(ordered_steps) and ordered_steps != [] -> %>
            <%= for {step, index} <- Enum.with_index(ordered_steps) do %>
              <div class="flex items-center justify-between p-3 border border-gray-200 rounded-md bg-gray-50">
                <div class="flex items-center space-x-3">
                  <div class="flex-shrink-0 w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
                    <span class="text-sm font-medium text-indigo-600"><%= step.step_number %></span>
                  </div>
                  <div class="text-sm text-gray-900">
                    <div class="font-medium"><%= step.form_info.name %></div>
                    <div class="text-xs text-gray-500"><%= step.form_info.type %> • <%= step.form_info.fields_count %> fields</div>
                  </div>
                </div>

                <div class="flex items-center space-x-1">
                  <button
                    phx-click="move_step_up"
                    phx-value-step-id={step.id}
                    phx-target={@myself}
                    disabled={index == 0}
                    class={[
                      "p-1 rounded text-xs transition-colors",
                      if(index == 0,
                        do: "text-gray-300 cursor-not-allowed",
                        else: "text-gray-600 hover:text-gray-900 hover:bg-gray-200"
                      )
                    ]}
                    title="Move step up"
                  >
                    <.icon name="hero-chevron-up" class="h-4 w-4" />
                  </button>

                  <button
                    phx-click="move_step_down"
                    phx-value-step-id={step.id}
                    phx-target={@myself}
                    disabled={index == length(ordered_steps) - 1}
                    class={[
                      "p-1 rounded text-xs transition-colors",
                      if(index == length(ordered_steps) - 1,
                        do: "text-gray-300 cursor-not-allowed",
                        else: "text-gray-600 hover:text-gray-900 hover:bg-gray-200"
                      )
                    ]}
                    title="Move step down"
                  >
                    <.icon name="hero-chevron-down" class="h-4 w-4" />
                  </button>

                  <button
                    phx-click="remove_step"
                    phx-value-step-id={step.id}
                    phx-target={@myself}
                    data-confirm="Are you sure you want to remove this form from the wizard?"
                    class="p-1 rounded text-xs transition-colors text-red-600 hover:text-red-900 hover:bg-red-100"
                    title="Remove step"
                  >
                    <.icon name="hero-trash" class="h-4 w-4" />
                  </button>
                </div>
              </div>
            <% end %>
          <% %{result: ordered_steps} when is_list(ordered_steps) and ordered_steps == [] -> %>
            <div class="text-center py-8">
              <p class="text-gray-500">No steps configured yet. Add forms to get started.</p>
            </div>
          <% %{result: nil} -> %>
            <div class="text-center py-8">
              <p class="text-gray-500">Loading wizard steps...</p>
            </div>
          <% _ -> %>
            <div class="text-center py-8">
              <p class="text-gray-500">Loading wizard steps...</p>
            </div>
        <% end %>
      </div>

      <!-- Reorder Modal -->
      <%= if @show_reorder_modal do %>
        <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" phx-click="hide_reorder_modal" phx-target={@myself}>
          <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white" phx-click-away="hide_reorder_modal" phx-target={@myself}>
            <div class="mt-3">
              <!-- Modal Header -->
              <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Reorder Wizard Steps</h3>
                <button
                  phx-click="hide_reorder_modal"
                  phx-target={@myself}
                  class="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <.icon name="hero-x-mark" class="h-5 w-5" />
                </button>
              </div>

              <!-- Steps List -->
              <div class="space-y-2 mb-4">
                <%= case @ordered_steps do %>
                  <% %{result: ordered_steps} when is_list(ordered_steps) and ordered_steps != [] -> %>
                    <%= for {step, index} <- Enum.with_index(ordered_steps) do %>
                  <div class="flex items-center justify-between p-3 border border-gray-200 rounded-md bg-gray-50">
                    <div class="flex items-center space-x-3">
                      <span class="text-sm font-medium text-gray-600">Step <%= step.step_number %></span>
                      <div class="text-sm text-gray-900">
                        <div class="font-medium"><%= step.form_info.name %></div>
                        <div class="text-xs text-gray-500"><%= step.form_info.type %> • <%= step.form_info.fields_count %> fields</div>
                      </div>
                    </div>
                    
                    <div class="flex items-center space-x-1">
                      <button
                        phx-click="move_step_up"
                        phx-value-step-id={step.id}
                        phx-target={@myself}
                        disabled={index == 0}
                        class={[
                          "p-1 rounded text-xs transition-colors",
                          if(index == 0,
                            do: "text-gray-300 cursor-not-allowed",
                            else: "text-gray-600 hover:text-gray-900 hover:bg-gray-200"
                          )
                        ]}
                      >
                        <.icon name="hero-chevron-up" class="h-4 w-4" />
                      </button>
                      
                      <button
                        phx-click="move_step_down"
                        phx-value-step-id={step.id}
                        phx-target={@myself}
                        disabled={index == length(ordered_steps) - 1}
                        class={[
                          "p-1 rounded text-xs transition-colors",
                          if(index == length(ordered_steps) - 1,
                            do: "text-gray-300 cursor-not-allowed",
                            else: "text-gray-600 hover:text-gray-900 hover:bg-gray-200"
                          )
                        ]}
                      >
                        <.icon name="hero-chevron-down" class="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                    <% end %>
                  <% %{result: ordered_steps} when is_list(ordered_steps) and ordered_steps == [] -> %>
                    <div class="text-center py-4">
                      <p class="text-gray-500 text-sm">No steps to reorder</p>
                    </div>
                  <% %{result: nil} -> %>
                    <div class="text-center py-4">
                      <p class="text-gray-500 text-sm">Loading steps...</p>
                    </div>
                  <% _ -> %>
                    <div class="text-center py-4">
                      <p class="text-gray-500 text-sm">Loading steps...</p>
                    </div>
                <% end %>
              </div>

              <!-- Modal Actions -->
              <div class="flex items-center justify-end space-x-3">
                <button
                  phx-click="hide_reorder_modal"
                  phx-target={@myself}
                  class="px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      <% end %>

      <!-- Add Form Modal -->
      <%= if @show_add_form_modal do %>
        <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" phx-click="hide_add_form_modal" phx-target={@myself}>
          <div class="relative top-10 mx-auto p-5 border max-w-4xl shadow-lg rounded-md bg-white" phx-click-away="hide_add_form_modal" phx-target={@myself}>
            <div class="mt-3">
              <!-- Modal Header -->
              <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Add Form to Wizard</h3>
                <button
                  phx-click="hide_add_form_modal"
                  phx-target={@myself}
                  class="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <.icon name="hero-x-mark" class="h-5 w-5" />
                </button>
              </div>

              <!-- Search -->
              <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Search Forms</label>
                <input
                  type="text"
                  placeholder="Search by form name..."
                  value={@form_search_query}
                  phx-keyup="search_forms"
                  phx-target={@myself}
                  class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                />
              </div>

              <!-- Available Forms -->
              <%= if Enum.empty?(@filtered_available_forms) do %>
                <div class="text-center py-12">
                  <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <%= if @form_search_query != "" do %>
                      <.icon name="hero-magnifying-glass" class="h-8 w-8 text-gray-400" />
                    <% else %>
                      <.icon name="hero-document-text" class="h-8 w-8 text-gray-400" />
                    <% end %>
                  </div>
                  <h4 class="text-md font-medium text-gray-900 mb-2">
                    <%= if @form_search_query != "" do %>
                      No forms match your search
                    <% else %>
                      No available forms
                    <% end %>
                  </h4>
                  <p class="text-gray-600 mb-4">
                    <%= if @form_search_query != "" do %>
                      Try adjusting your search terms
                    <% else %>
                      All mobile forms are already added to this wizard
                    <% end %>
                  </p>
                </div>
              <% else %>
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto">
                  <%= case @available_mobile_forms do %>
                    <% %{result: forms} when is_list(forms) -> %>
                      <%= for form <- @filtered_available_forms do %>
                    <div class="relative rounded-lg border-2 border-gray-200 p-4 cursor-pointer transition-all duration-200 hover:border-indigo-300 hover:bg-indigo-50">
                      <button
                        phx-click="add_form_to_wizard"
                        phx-value-form_id={form.id}
                        phx-target={@myself}
                        class="w-full text-left"
                      >
                        <div class="flex items-start space-x-3">
                          <div class="flex-shrink-0">
                            <.icon name="hero-device-phone-mobile" class="h-6 w-6 text-blue-500" />
                          </div>
                          <div class="flex-1 min-w-0">
                            <p class="text-sm font-medium text-gray-900 truncate">
                              <%= form.name %>
                            </p>
                            <p class="text-xs text-gray-500 mt-1">
                              Type: <%= String.capitalize(form.type) %>
                            </p>
                            <p class="text-xs text-gray-500">
                              Page: <%= form.page.name %> | Screen: <%= form.page.screen.name %>
                            </p>
                            <%= if form.validation_schema do %>
                              <div class="mt-2">
                                <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-green-100 text-green-800">
                                  <.icon name="hero-check" class="h-3 w-3 mr-1" />
                                  Has Validation
                                </span>
                              </div>
                            <% else %>
                              <div class="mt-2">
                                <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                                  <.icon name="hero-exclamation-triangle" class="h-3 w-3 mr-1" />
                                  No Validation
                                </span>
                              </div>
                            <% end %>
                          </div>
                        </div>
                      </button>
                    </div>
                      <% end %>
                    <% _ -> %>
                      <!-- Loading or no forms -->
                  <% end %>
                </div>
              <% end %>

              <!-- Modal Actions -->
              <div class="flex items-center justify-end space-x-3 mt-6 pt-4 border-t">
                <button
                  phx-click="hide_add_form_modal"
                  phx-target={@myself}
                  class="px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      <% end %>

      <!-- Data Mappings Management Modal -->
      <%= if @show_data_mappings_modal && @selected_step_for_mapping do %>
        <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div class="relative top-10 mx-auto p-5 border max-w-5xl shadow-lg rounded-md bg-white">
            <div class="mt-3">
              <!-- Modal Header -->
              <div class="flex items-center justify-between mb-4">
                <div>
                  <h3 class="text-lg font-medium text-gray-900">
                    Data Mappings for Step <%= @selected_step_for_mapping.step_number %>
                  </h3>
                  <p class="text-sm text-gray-500 mt-1"><%= @selected_step_for_mapping.form_info.name %></p>
                </div>
                <button
                  phx-click="close_data_mappings_modal"
                  phx-target={@myself}
                  class="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <.icon name="hero-x-mark" class="h-5 w-5" />
                </button>
              </div>

              <!-- Mapping Form -->
              <%= if @show_mapping_form do %>
                <div class="bg-white border border-gray-200 rounded-lg p-6 mb-6">
                  <div class="flex items-center justify-between mb-4">
                    <h5 class="text-sm font-medium text-gray-900">
                      <%= if @mapping_action == :edit, do: "Edit", else: "Add" %> Data Mapping
                    </h5>
                    <button
                      phx-click="hide_mapping_form"
                      phx-target={@myself}
                      class="text-gray-400 hover:text-gray-600 transition-colors"
                    >
                      <.icon name="hero-x-mark" class="h-5 w-5" />
                    </button>
                  </div>

                  <.simple_form
                    for={%{}}
                    as={:data_mapping}
                    phx-submit="save_data_mapping"
                    phx-target={@myself}
                    class="space-y-4"
                  >
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <!-- Source Step Selection -->
                      <.input
                        name="data_mapping[source_step_number]"
                        type="select"
                        label="Source Step"
                        options={[{"Select source step...", ""}] ++ Enum.map(@available_source_steps, &{"Step #{&1.step_number}: #{&1.form_name}", &1.step_number})}
                        value={@selected_mapping && @selected_mapping.source_step_number || ""}
                        required
                        help="Previous step to copy data from"
                      />

                      <!-- Target Field Selection -->
                      <.input
                        name="data_mapping[target_field_id]"
                        type="select"
                        label="Target Field"
                        options={[{"Select target field...", ""}] ++ Enum.map(@available_target_fields, &{"#{&1.label} (#{&1.field_name})", &1.id})}
                        value={@selected_mapping && @selected_mapping.target_field_id || ""}
                        required
                        help="Field in current step to populate"
                      />
                    </div>

                    <!-- JSON Path -->
                    <.input
                      name="data_mapping[source_json_path]"
                      type="text"
                      label="JSON Path Pattern"
                      placeholder="e.g., field_name or nested.field or array[0].field"
                      value={@selected_mapping && @selected_mapping.source_json_path || ""}
                      required
                      help="Path to the data within the source step (supports dot notation)"
                    />

                    <!-- Pattern Examples -->
                    <div class="bg-blue-50 border border-blue-200 rounded-md p-3">
                      <h6 class="text-xs font-medium text-blue-900 mb-2">Pattern Examples:</h6>
                      <ul class="text-xs text-blue-800 space-y-1">
                        <li><code>user_name</code> - Copy field "user_name" directly</li>
                        <li><code>personal.email</code> - Copy nested field "email" from "personal" object</li>
                        <li><code>addresses[0].street</code> - Copy "street" from first address in array</li>
                      </ul>
                    </div>

                    <!-- Active Toggle -->
                    <.input
                      name="data_mapping[active]"
                      type="checkbox"
                      label="Active"
                      checked={!@selected_mapping || @selected_mapping.active}
                    />

                    <input type="hidden" name="data_mapping[wizard_step_id]" value={@selected_step_for_mapping.id} />
                    <%= if @selected_mapping do %>
                      <input type="hidden" name="data_mapping[id]" value={@selected_mapping.id} />
                    <% end %>

                    <:actions>
                      <.button
                        type="button"
                        phx-click="hide_mapping_form"
                        phx-target={@myself}
                        class="px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors"
                      >
                        Cancel
                      </.button>
                      <.button type="submit" phx-disable-with="Saving...">
                        <%= if @mapping_action == :edit, do: "Update", else: "Add" %> Mapping
                      </.button>
                    </:actions>
                  </.simple_form>
                </div>
              <% end %>

              <!-- Add Mapping Button -->
              <%= if not @show_mapping_form do %>
                <div class="mb-6">
                  <button
                    phx-click="show_mapping_form"
                    phx-target={@myself}
                    class="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 transition-colors"
                  >
                    <.icon name="hero-plus" class="h-4 w-4 mr-2" />
                    Add Data Mapping
                  </button>
                </div>
              <% end %>

              <!-- Existing Mappings List -->
              <%= if length(@step_data_mappings) > 0 do %>
                <div class="space-y-3">
                  <h5 class="text-sm font-medium text-gray-900">Configured Mappings</h5>
                  <%= for mapping <- @step_data_mappings do %>
                    <div class="flex items-center justify-between p-3 bg-gray-50 border border-gray-200 rounded-md">
                      <div class="flex items-center space-x-3">
                        <div class="flex-shrink-0">
                          <.icon name="hero-arrow-path" class={[
                            "h-4 w-4",
                            if(mapping.active, do: "text-green-600", else: "text-gray-400")
                          ]} />
                        </div>
                        <div class="flex-1 min-w-0">
                          <div class="flex items-center space-x-2">
                            <p class="text-sm font-medium text-gray-900">
                              Step <%= mapping.source_step_number %> → <%= mapping.target_field.label %>
                            </p>
                            <%= if not mapping.active do %>
                              <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                                Inactive
                              </span>
                            <% end %>
                          </div>
                          <div class="flex items-center space-x-2 mt-1">
                            <code class="text-xs bg-gray-200 px-2 py-1 rounded font-mono"><%= mapping.source_json_path %></code>
                            <span class="text-xs text-gray-500">→ <%= mapping.target_field.field_name %></span>
                          </div>
                        </div>
                      </div>
                      <div class="flex items-center space-x-2">
                        <button
                          phx-click="edit_data_mapping"
                          phx-value-id={mapping.id}
                          phx-target={@myself}
                          class="text-xs text-indigo-600 hover:text-indigo-500"
                        >
                          Edit
                        </button>
                        <button
                          phx-click="delete_data_mapping"
                          phx-value-id={mapping.id}
                          phx-target={@myself}
                          data-confirm="Are you sure you want to remove this data mapping?"
                          class="text-xs text-red-600 hover:text-red-500"
                        >
                          Remove
                        </button>
                      </div>
                    </div>
                  <% end %>
                </div>
              <% else %>
                <div class="text-center py-8">
                  <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <.icon name="hero-arrow-path" class="h-8 w-8 text-gray-400" />
                  </div>
                  <h4 class="text-md font-medium text-gray-900 mb-2">No Data Mappings</h4>
                  <p class="text-gray-600 mb-4">Add data mappings to automatically populate fields from previous steps</p>
                </div>
              <% end %>

              <!-- Modal Actions -->
              <div class="flex items-center justify-end space-x-3 mt-6 pt-4 border-t">
                <button
                  phx-click="close_data_mappings_modal"
                  phx-target={@myself}
                  class="px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      <% end %>

    </div>
    """
  end

  @impl true
  def handle_async(:available_mobile_forms, {:ok, forms}, socket) do
    # Filter out forms already in this wizard when forms are loaded
    case socket.assigns.ordered_steps do
      %{result: ordered_steps} ->
        current_form_ids = Enum.map(ordered_steps, & &1.mobile_form_id)
        filtered_forms = Enum.reject(forms, &(&1.id in current_form_ids))
        {:noreply, assign(socket, :filtered_available_forms, filtered_forms)}
      _ ->
        {:noreply, assign(socket, :filtered_available_forms, forms)}
    end
  end

  @impl true
  def handle_async(:ordered_steps, {:ok, _ordered_steps}, socket) do
    # Update filtered forms when ordered steps are loaded
    case socket.assigns.available_mobile_forms do
      %{result: forms} ->
        case socket.assigns.ordered_steps do
          %{result: ordered_steps} ->
            current_form_ids = Enum.map(ordered_steps, & &1.mobile_form_id)
            filtered_forms = Enum.reject(forms, &(&1.id in current_form_ids))
            {:noreply, assign(socket, :filtered_available_forms, filtered_forms)}
          _ ->
            {:noreply, socket}
        end
      _ ->
        {:noreply, socket}
    end
  end

  @impl true
  def handle_async(:ordered_steps, {:error, _reason}, socket) do
    # Handle ordered_steps async errors by setting empty list
    {:noreply, assign(socket, :ordered_steps, %{result: []})}
  end

  @impl true
  def handle_async(_name, {:ok, _result}, socket) do
    # Handle any other async results
    {:noreply, socket}
  end

  @impl true
  def handle_async(_name, {:exit, reason}, socket) do
    # Handle async process exits
    {:noreply, put_flash(socket, :error, "Failed to load data: #{inspect(reason)}")}
  end

  @impl true
  def handle_event("show_reorder_modal", _params, socket) do
    {:noreply, assign(socket, :show_reorder_modal, true)}
  end

  @impl true
  def handle_event("hide_reorder_modal", _params, socket) do
    {:noreply, assign(socket, :show_reorder_modal, false)}
  end

  @impl true
  def handle_event("show_add_form_modal", _params, socket) do
    {:noreply, assign(socket, :show_add_form_modal, true)}
  end

  @impl true
  def handle_event("hide_add_form_modal", _params, socket) do
    {:noreply, assign(socket, :show_add_form_modal, false)}
  end

  @impl true
  def handle_event("search_forms", %{"value" => query}, socket) do
    filtered_forms = if query == "" do
      socket.assigns.available_mobile_forms
    else
      socket.assigns.available_mobile_forms
      |> Enum.filter(&String.contains?(String.downcase(&1.name), String.downcase(query)))
    end

    socket =
      socket
      |> assign(:form_search_query, query)
      |> assign(:filtered_available_forms, filtered_forms)

    {:noreply, socket}
  end

  @impl true
  def handle_event("add_form_to_wizard", %{"form_id" => form_id}, socket) do
    wizard_id = socket.assigns.wizard.id

    # Calculate the next step number - handle async result structure
    current_steps = case socket.assigns.ordered_steps do
      %{result: steps} when is_list(steps) -> steps
      _ -> []
    end
    next_step_number = length(current_steps) + 1
    
    case DynamicFormsManager.add_mobile_wizard_step(wizard_id, form_id, next_step_number) do
      {:ok, _step} ->
        # Refresh data
        updated_socket = refresh_component_data(socket)
        send(self(), :refresh_wizard_data)
        
        {:noreply, 
         updated_socket
         |> assign(:show_add_form_modal, false)
         |> put_flash(:info, "Form added to wizard successfully")}
      
      {:error, reason} ->
        {:noreply, put_flash(socket, :error, "Failed to add form: #{inspect(reason)}")}
    end
  end

  @impl true
  def handle_event("remove_step", %{"step-id" => step_id}, socket) do
    step_id = String.to_integer(step_id)
    
    case DynamicFormsManager.remove_wizard_step_and_reorder(step_id) do
      {:ok, :ok} ->
        # Refresh data
        updated_socket = refresh_component_data(socket)
        send(self(), :refresh_wizard_data)
        
        {:noreply, 
         updated_socket
         |> put_flash(:info, "Form removed from wizard successfully")}
      
      {:error, reason} ->
        {:noreply, put_flash(socket, :error, "Failed to remove form: #{inspect(reason)}")}
    end
  end

  @impl true
  def handle_event("move_step_up", %{"step-id" => step_id}, socket) do
    step_id = String.to_integer(step_id)

    case DynamicFormsManager.move_wizard_step_up(step_id) do
      {:ok, :ok} ->
        # Refresh the ordered steps while maintaining async result structure
        ordered_steps = DynamicFormsManager.get_wizard_steps_ordered(socket.assigns.wizard.id)
        send(self(), :refresh_wizard_data)

        {:noreply, assign(socket, :ordered_steps, %{result: ordered_steps})}

      {:error, reason} ->
        {:noreply, put_flash(socket, :error, "Failed to move step: #{reason}")}
    end
  end

  @impl true
  def handle_event("move_step_down", %{"step-id" => step_id}, socket) do
    step_id = String.to_integer(step_id)

    case DynamicFormsManager.move_wizard_step_down(step_id) do
      {:ok, :ok} ->
        # Refresh the ordered steps while maintaining async result structure
        ordered_steps = DynamicFormsManager.get_wizard_steps_ordered(socket.assigns.wizard.id)
        send(self(), :refresh_wizard_data)

        {:noreply, assign(socket, :ordered_steps, %{result: ordered_steps})}

      {:error, reason} ->
        {:noreply, put_flash(socket, :error, "Failed to move step: #{reason}")}
    end
  end

  # Tab navigation event handlers
  @impl true
  def handle_event("switch_tab", %{"tab" => tab}, socket) do
    current_tab = String.to_atom(tab)
    {:noreply, assign(socket, :current_tab, current_tab)}
  end

  @impl true
  def handle_event("show_trigger_form", _params, socket) do
    {:noreply, 
     socket
     |> assign(:show_trigger_form, true)
     |> assign(:selected_trigger, nil)
     |> assign(:trigger_action, :new)}
  end

  @impl true
  def handle_event("hide_trigger_form", _params, socket) do
    {:noreply, 
     socket
     |> assign(:show_trigger_form, false)
     |> assign(:selected_trigger, nil)}
  end

  @impl true
  def handle_event("edit_wizard_trigger", %{"id" => trigger_id}, socket) do
    case TriggerContext.get_wizard_trigger!(trigger_id) do
      trigger ->
        {:noreply,
         socket
         |> assign(:show_trigger_form, true)
         |> assign(:selected_trigger, trigger)
         |> assign(:trigger_action, :edit)}
    end
  end

  @impl true
  def handle_event("delete_wizard_trigger", %{"id" => trigger_id}, socket) do
    case TriggerContext.get_wizard_trigger!(trigger_id) do
      trigger ->
        case TriggerContext.delete_wizard_trigger(trigger) do
          {:ok, _} ->
            updated_socket = refresh_trigger_data(socket)
            
            {:noreply,
             updated_socket
             |> put_flash(:info, "Trigger removed from wizard successfully")}

          {:error, _} ->
            {:noreply, put_flash(socket, :error, "Failed to remove trigger")}
        end
    end
  end

  @impl true
  def handle_event("save_wizard_trigger", %{"wizard_trigger" => params}, socket) do
    # Parse JSON fields
    parsed_params = 
      params
      |> parse_json_field("data_mapping")
      |> parse_json_field("conditions")
      |> Map.put("enabled", Map.get(params, "enabled") == "true")

    case socket.assigns.trigger_action do
      :new ->
        case TriggerContext.create_wizard_trigger(parsed_params) do
          {:ok, _trigger} ->
            updated_socket = refresh_trigger_data(socket)
            
            {:noreply,
             updated_socket
             |> assign(:show_trigger_form, false)
             |> assign(:selected_trigger, nil)
             |> put_flash(:info, "Trigger added to wizard successfully")}

          {:error, changeset} ->
            {:noreply, put_flash(socket, :error, "Failed to add trigger: #{inspect(changeset.errors)}")}
        end

      :edit ->
        case TriggerContext.update_wizard_trigger(socket.assigns.selected_trigger, parsed_params) do
          {:ok, _trigger} ->
            updated_socket = refresh_trigger_data(socket)
            
            {:noreply,
             updated_socket
             |> assign(:show_trigger_form, false)
             |> assign(:selected_trigger, nil)
             |> put_flash(:info, "Trigger updated successfully")}

          {:error, changeset} ->
            {:noreply, put_flash(socket, :error, "Failed to update trigger: #{inspect(changeset.errors)}")}
        end
    end
  end

  @impl true
  def handle_event("generate_sample_mapping", _params, socket) do
    # This would generate a sample mapping - for now just return success
    {:noreply, socket}
  end

  @impl true
  def handle_event("timing_changed", _params, socket) do
    # Handle timing changes if needed for UI updates
    {:noreply, socket}
  end

  @impl true
  def handle_event("validate_trigger_form", %{"wizard_trigger" => _params}, socket) do
    # Handle form validation without closing modal
    {:noreply, socket}
  end

  # Data mapping event handlers
  @impl true
  def handle_event("manage_step_data_mappings", %{"step_id" => step_id}, socket) do
    step_id = String.to_integer(step_id)
    selected_step = case socket.assigns.ordered_steps do
      %{result: steps} -> Enum.find(steps, &(&1.id == step_id))
      _ -> nil
    end
    
    # Load data mappings for the selected step
    step_data_mappings = DynamicFormsManager.get_step_data_mappings(step_id)
    
    # Load available target fields
    available_target_fields = case DynamicFormsManager.get_available_fields_for_mapping(step_id) do
      {:ok, fields} -> fields
      {:error, _} -> []
    end
    
    # Load available source steps (previous steps)
    available_source_steps = DynamicFormsManager.get_available_source_steps(socket.assigns.wizard.id, selected_step.step_number)
    
    {:noreply,
     socket
     |> assign(:show_data_mappings_modal, true)
     |> assign(:selected_step_for_mapping, selected_step)
     |> assign(:step_data_mappings, step_data_mappings)
     |> assign(:available_target_fields, available_target_fields)
     |> assign(:available_source_steps, available_source_steps)
     |> assign(:show_mapping_form, false)
     |> assign(:selected_mapping, nil)
     |> assign(:mapping_action, :new)}
  end

  @impl true
  def handle_event("close_data_mappings_modal", _params, socket) do
    {:noreply,
     socket
     |> assign(:show_data_mappings_modal, false)
     |> assign(:selected_step_for_mapping, nil)
     |> assign(:step_data_mappings, [])
     |> assign(:available_target_fields, [])
     |> assign(:available_source_steps, [])
     |> assign(:show_mapping_form, false)
     |> assign(:selected_mapping, nil)}
  end

  @impl true
  def handle_event("show_mapping_form", _params, socket) do
    {:noreply,
     socket
     |> assign(:show_mapping_form, true)
     |> assign(:selected_mapping, nil)
     |> assign(:mapping_action, :new)}
  end

  @impl true
  def handle_event("hide_mapping_form", _params, socket) do
    {:noreply,
     socket
     |> assign(:show_mapping_form, false)
     |> assign(:selected_mapping, nil)}
  end

  @impl true
  def handle_event("save_data_mapping", %{"data_mapping" => params}, socket) do
    parsed_params = Map.put(params, "active", Map.get(params, "active") == "true")
    
    case socket.assigns.mapping_action do
      :new ->
        case DynamicFormsManager.create_step_data_mapping(parsed_params) do
          {:ok, _mapping} ->
            # Refresh data mappings
            updated_mappings = DynamicFormsManager.get_step_data_mappings(socket.assigns.selected_step_for_mapping.id)
            
            {:noreply,
             socket
             |> assign(:step_data_mappings, updated_mappings)
             |> assign(:show_mapping_form, false)
             |> assign(:selected_mapping, nil)
             |> put_flash(:info, "Data mapping added successfully")}

          {:error, changeset} ->
            {:noreply, put_flash(socket, :error, "Failed to add data mapping: #{inspect(changeset.errors)}")}
        end

      :edit ->
        case DynamicFormsManager.update_step_data_mapping(socket.assigns.selected_mapping, parsed_params) do
          {:ok, _mapping} ->
            # Refresh data mappings
            updated_mappings = DynamicFormsManager.get_step_data_mappings(socket.assigns.selected_step_for_mapping.id)
            
            {:noreply,
             socket
             |> assign(:step_data_mappings, updated_mappings)
             |> assign(:show_mapping_form, false)
             |> assign(:selected_mapping, nil)
             |> put_flash(:info, "Data mapping updated successfully")}

          {:error, changeset} ->
            {:noreply, put_flash(socket, :error, "Failed to update data mapping: #{inspect(changeset.errors)}")}
        end
    end
  end

  @impl true
  def handle_event("edit_data_mapping", %{"id" => mapping_id}, socket) do
    mapping_id = String.to_integer(mapping_id)
    mapping = DynamicFormsManager.get_step_data_mapping!(mapping_id)
    
    {:noreply,
     socket
     |> assign(:show_mapping_form, true)
     |> assign(:selected_mapping, mapping)
     |> assign(:mapping_action, :edit)}
  end

  @impl true
  def handle_event("delete_data_mapping", %{"id" => mapping_id}, socket) do
    mapping_id = String.to_integer(mapping_id)
    mapping = DynamicFormsManager.get_step_data_mapping!(mapping_id)
    
    case DynamicFormsManager.delete_step_data_mapping(mapping) do
      {:ok, _} ->
        # Refresh data mappings
        updated_mappings = DynamicFormsManager.get_step_data_mappings(socket.assigns.selected_step_for_mapping.id)
        
        {:noreply,
         socket
         |> assign(:step_data_mappings, updated_mappings)
         |> put_flash(:info, "Data mapping removed successfully")}

      {:error, _} ->
        {:noreply, put_flash(socket, :error, "Failed to remove data mapping")}
    end
  end

  # Helper function to refresh component data
  defp refresh_component_data(socket) do
    wizard_id = socket.assigns.wizard.id
    ordered_steps = DynamicFormsManager.get_wizard_steps_ordered(wizard_id)
    
    # Get available mobile forms for adding to wizard
    available_mobile_forms = case DynamicFormsManager.list_mobile_forms_for_wizard() do
      {:ok, forms} -> 
        # Filter out forms already in this wizard
        current_form_ids = Enum.map(ordered_steps, & &1.mobile_form_id)
        Enum.reject(forms, &(&1.id in current_form_ids))
      {:error, _} -> 
        []
    end
    
    # Apply current search filter
    filtered_forms = if socket.assigns.form_search_query == "" do
      available_mobile_forms
    else
      available_mobile_forms
      |> Enum.filter(&String.contains?(String.downcase(&1.name), String.downcase(socket.assigns.form_search_query)))
    end

    socket
    |> assign(:ordered_steps, %{result: ordered_steps})
    |> assign(:available_mobile_forms, available_mobile_forms)
    |> assign(:filtered_available_forms, filtered_forms)
  end

  # Helper function to refresh trigger data
  defp refresh_trigger_data(socket) do
    wizard_triggers = TriggerContext.list_wizard_triggers(socket.assigns.wizard.id)
    assign(socket, :wizard_triggers, wizard_triggers)
  end

  # Helper function to parse JSON fields in params
  defp parse_json_field(params, field_name) do
    case Map.get(params, field_name) do
      nil -> params
      "" -> Map.put(params, field_name, %{})
      json_string when is_binary(json_string) ->
        try do
          parsed = Jason.decode!(json_string)
          Map.put(params, field_name, parsed)
        rescue
          Jason.DecodeError ->
            # If JSON is invalid, leave as empty map
            Map.put(params, field_name, %{})
        end
      value -> Map.put(params, field_name, value)
    end
  end
end