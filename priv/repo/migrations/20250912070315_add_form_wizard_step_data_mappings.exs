defmodule ServiceManager.Repo.Migrations.AddFormWizardStepDataMappings do
  use Ecto.Migration

  def change do
    create table(:form_wizard_step_data_mappings) do
      add :wizard_step_id, references(:form_wizard_steps, on_delete: :delete_all), null: false
      add :target_field_id, references(:mobile_form_fields_v2, type: :uuid, on_delete: :delete_all), null: false
      add :source_step_number, :integer, null: false
      add :source_json_path, :string, null: false
      add :active, :boolean, default: true, null: false

      timestamps()
    end

    create index(:form_wizard_step_data_mappings, [:wizard_step_id])
    create index(:form_wizard_step_data_mappings, [:target_field_id])
    create index(:form_wizard_step_data_mappings, [:source_step_number])
    create unique_index(:form_wizard_step_data_mappings, [:wizard_step_id, :target_field_id], 
           name: :form_wizard_step_data_mappings_unique_target_field)
  end
end
